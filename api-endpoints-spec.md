# Rumi Backend API Endpoints Specification

This document provides a comprehensive specification of all API endpoints across the Rumi backend services.

## Services Overview

The Rumi backend consists of multiple microservices:

- **Elio**: Main API gateway and orchestration service
- **Nebula**: AI/ML service for memory, summaries, and X-Ray functionality
- **Mars**: Data service for sessions, users, and analytics
- **Luxor**: Recording and integration service
- **Aurora**: Additional service functionality
- **Hubble**: Authentication and authorization service

## X-Ray Service Endpoints

### X-Ray Management

- `POST /v1.0/xrays/generate/prompt` - Generate X-Ray prompt (Step 1)
- `POST /v1.0/xrays/generate/info` - Generate X-Ray info (Step 2)
- `POST /v1.0/xrays` - Create X-Ray (Step 3)
- `GET /v1.0/xrays` - List X-Rays with pagination and filtering
- `GET /v1.0/xrays/id/:xrayID` - Get X-Ray by ID
- `PATCH /v1.0/xrays/id/:xrayID` - Update X-Ray
- `DELETE /v1.0/xrays/:xrayID` - Delete X-Ray
- `POST /v1.0/xrays/id/:xrayID/share` - Share X-Ray as template

### X-Ray Notifications

- `GET /v1.0/xrays/id/:xrayID/notifications` - Get X-Ray notifications
- `PATCH /v1.0/xrays/id/:xrayID/notifications/mark-seen` - Mark notifications as seen

### X-Ray Templates

- `GET /v1.0/xray-templates/:templateID` - Get X-Ray template by ID
- `GET /v1.0/xray-templates` - List X-Ray templates

## Authentication & Authorization (Hubble)

### Authentication

- `POST /v1.0/auth/guest` - Login guest user
- `POST /v1.0/auth/social-code` - Social login with authorization code
- `POST /v1.0/auth/social-token` - Social login with token
- `POST /v1.0/auth/request-otp` - Request OTP for email login
- `POST /v1.0/auth/verify-otp` - Verify email OTP
- `PUT /v1.0/auth/refresh-auth-token` - Refresh authentication token
- `POST /v1.0/auth/logout` - Logout user
- `DELETE /v1.0/auth/user` - Delete user account
- `PATCH /v1.0/auth/user/host-opt-in` - Host opt-in

### Authorization

- `POST /v1.0/auth/is-authorized` - Check authorization
- `POST /v1.0/auth/is-authorized-batch` - Batch authorization check
- `GET /v1.0/auth/is-authorized-feature` - Check feature authorization
- `GET /v1.0/auth/is-authorized-integration` - Check integration authorization

### OAuth2

- `POST /v1.0/oauth/exchange-code-for-token` - Exchange OAuth code for token
- `POST /v1.0/oauth/refresh-auth-token` - Refresh OAuth token
- `POST /v1.0/oauth/request-otp` - OAuth OTP request
- `POST /v1.0/oauth/social-code` - OAuth social login with code
- `POST /v1.0/oauth/verify-otp` - OAuth OTP verification
- `POST /v1.0/oauth2/client-credentials` - Issue OAuth2 client credentials

## Session Management (Meetings)

### Session CRUD

- `POST /v1.0/sessions` - Create session
- `GET /v1.0/sessions/future` - Get future sessions
- `GET /v1.0/sessions/past` - Get past sessions (deprecated)
- `GET /v1.0/sessions/meeting-memory` - Get meeting memory sessions (deprecated)
- `GET /v1.0/sessions/library/upcoming` - Get upcoming library sessions (deprecated)
- `PATCH /v1.0/sessions/{sessionID}/recurrence/{recurrenceID}` - Update session
- `PATCH /v1.0/sessions/{sessionID}/recurrence/{recurrenceID}/recur` - Recur session
- `PATCH /v1.0/sessions/{sessionID}/recurrence/{recurrenceID}/recur-at` - Recur session at time

### Session Access Control

- `POST /v1.0/sessions/access` - Update session access control rules
- `PUT /v1.0/sessions/access/add` - Add session access control rules
- `DELETE /v1.0/sessions/access/remove` - Remove session access control rules
- `POST /v1.0/sessions/access/request-access` - Request session access
- `PUT /v1.0/sessions/access/request` - Approve/deny session access request
- `GET /v1.0/sessions/access/in-review` - Get in-review access requests
- `GET /v1.0/sessions/access/grouped` - Get grouped access requests
- `POST /v1.0/sessions/access/get-in-review-access-requests-count` - Count in-review requests

### Session Users & Presence

- `GET /v1.0/sessions/users/{sessionID}/{recurrenceID}` - Get session users
- `GET /v1.0/sessions/presence/{id}` - Get session presence
- `POST /v1.0/sessions/guest` - Login guest user with session

### Session Recurrences

- `GET /v1.0/recurrences/past` - Get past session recurrences

### Session Integration

- `GET /v1.0/sessions/cards` - Get integration cards

## LiveKit Integration

### Room Management

- `GET /v1.0/meeting/{roomName}` - Fetch room metadata
- `POST /v1.0/meeting/{roomName}/end` - End room
- `GET /v1.0/meeting/{roomName}/participants/count` - Get participants count

### Participant Management

- `POST /v1.0/meeting/{id}/token` - Generate meeting token
- `GET /v1.0/meeting/{roomName}/participant/{identity}` - Fetch participant metadata
- `PATCH /v1.0/meeting/{room}/participant` - Patch participant state

## Memory & AI (Wormhole/Nebula)

### AI Interaction

- `POST /v1.0/memory/ai-stream` - AI streaming chat
- `POST /v1.0/memory/ask-ai-sync` - Synchronous AI query
- `POST /v1.0/memory/stop` - Stop AI generation
- `PATCH /v1.0/memory/feedback` - Submit user feedback

### Thread Management

- `GET /v1.0/memory/threads` - Get user threads
- `GET /v1.0/memory/threads/{threadID}` - Get thread by ID
- `DELETE /v1.0/memory/threads/{threadID}` - Delete thread
- `GET /v1.0/memory/threads/{threadID}/messages` - Get thread messages

### Suggestions

- `GET /v1.0/memory/suggestions` - Get AI suggestions

## Transcriptions

### Transcription Management

- `GET /v1.0/session/transcriptions` - Download transcriptions
- `GET /v1.0/transcriptions/audio-ingress` - Audio ingress websocket
- `GET /v1.0/transcriptions/speakers` - Fetch heard speakers

### Bot Integration

- `GET /v1.0/transcriptions/bot/calendar-user` - Get calendar user
- `POST /v1.0/transcriptions/bot/disconnect` - Disconnect calendar
- `GET /v1.0/transcriptions/bot/google-auth` - Generate bot auth URL
- `POST /v1.0/transcriptions/bot/webhook` - Recall transcriptions webhook

## Lobbies

### Lobby Management

- `POST /v1.0/lobbies` - Create lobby
- `PUT /v1.0/lobbies/slug` - Update lobby
- `GET /v1.0/lobbies/summary` - Get lobby summary
- `GET /v1.0/lobbies/enter` - Enter lobby (braidable)

### Lobby Participants

- `POST /v1.0/lobbies/add-participant` - Add participants to session
- `POST /v1.0/lobbies/start-session` - Start lobby session

## User Management

### User Operations

- `PUT /v1.0/users/id/{userID}` - Update user by ID
- `DELETE /v1.0/users/me` - Delete current user
- `GET /v1.0/users/me` - Get current user info

### User Billing

- `GET /v1.0/users/id/{userID}/plan` - Get user plan
- `GET /v1.0/users/id/{userID}/payment-method-details` - Get payment method details
- `GET /v1.0/users/id/{userID}/transactions` - List user transactions
- `GET /v1.0/users/id/{userID}/update-payment-method-transaction` - Get payment method update transaction

## Billing

### Plans & Pricing

- `GET /v1.0/billing/plans` - List available plans

## Post-Session Summaries

### Summary Management

- `DELETE /v1.0/post-session-summaries/by-session-ids/{sessionID}` - Delete post-session summary

## AI Feed

### Feed Management

- `POST /v1.0/ai-feed/event` - Create AI feed event
- `GET /v1.0/ai-feed/get` - Get AI feed

## Chat

### Chat Tokens

- `GET /v1.0/chat/token` - Get chat token

## Integrations

### Integration Management

- `POST /v1.0/integrations/events` - Post integration event
- `GET /v1.0/integrations/-/get-bindings-by-owner-session-recurrence-per-provider` - Get integration bindings

## Utility Endpoints

### Health & Status

- `GET /v1.0/ws-health` - WebSocket health check
- `GET /v1.0/time` - Time ping endpoint

### Fallback

- `*` `/v1.0/{fallback}` - Wormhole router fallback (all HTTP methods)

## External Service Proxies

### Nango Integration

- `*` `/v1.0/nango/*` - Proxy to Luxor public endpoints

### Billing Proxy

- `*` `/v1.0/billing/*` - Proxy to Mars billing endpoints

### Recording Proxy (v2.0)

- `GET /v2.0/recordings/{sessionID}/*` - Proxy to Luxor recordings

## Meeting Types

- `GET /v1.0/meeting-types` - Get all meeting types

## Data Models & Request/Response Structures

### X-Ray Data Models

#### XRayDTO

```json
{
  "id": "integer",
  "ownerId": "integer",
  "title": "string",
  "description": "string",
  "prompt": "string",
  "icon": "string",
  "shortSummary": "string",
  "currentCommitId": "integer (optional)",
  "currentCommit": "XRayDocCommit (optional)",
  "alertChannels": "map[string]bool",
  "isActive": "boolean",
  "visibility": "string",
  "type": "string",
  "scope": "string",
  "unreadNotificationsCount": "integer (optional)",
  "frequency": "string (optional)",
  "lastDigestAt": "integer (optional)",
  "createdAt": "integer",
  "updatedAt": "integer"
}
```

#### X-Ray Creation Requests

- **Step 1 - Generate Prompt**: `{"description": "string"}`
- **Step 2 - Generate Info**: `{"type": "string", "prompt": "string"}`
- **Step 3 - Create X-Ray**: `{"description": "string", "type": "string", "prompt": "string", "title": "string", "icon": "string", "shortSummary": "string", "frequency": "string", "alertChannels": "map[string]bool"}`

#### X-Ray Update Request

```json
{
  "title": "string (optional)",
  "icon": "string (optional)",
  "prompt": "string (optional)",
  "alertChannels": "map[string]bool (optional)",
  "isActive": "boolean (optional)",
  "frequency": "string (optional)"
}
```

### Session Data Models

#### SessionDTO

```json
{
  "sessionID": "string",
  "recurrenceID": "string",
  "title": "string",
  "description": "string",
  "startTime": "integer",
  "endTime": "integer",
  "hostUserID": "string",
  "isActive": "boolean",
  "isPubliclyVisible": "boolean",
  "accessRules": "SessionAccessRuleDTO[]"
}
```

#### CreateSessionRequest

```json
{
  "title": "string",
  "description": "string",
  "startTime": "integer",
  "endTime": "integer",
  "hostAccessRules": "SessionAccessRulesDTO[]",
  "viewerAccessRules": "SessionAccessRulesDTO[]",
  "isPubliclyVisible": "boolean",
  "isViewerAccessRestricted": "boolean (optional)",
  "userID": "string (optional)",
  "lobbyID": "string (optional)"
}
```

### User Data Models

#### UserDTO

```json
{
  "id": "string",
  "email": "string",
  "firstName": "string",
  "lastName": "string",
  "profilePictureURL": "string (optional)",
  "createdAt": "integer",
  "updatedAt": "integer"
}
```

### Authentication Models

#### LoginRequest

```json
{
  "email": "string",
  "password": "string (optional)",
  "provider": "string (optional)",
  "code": "string (optional)",
  "token": "string (optional)"
}
```

#### AuthResponse

```json
{
  "success": "boolean",
  "data": {
    "user": "UserDTO",
    "accessToken": "string",
    "refreshToken": "string",
    "expiresIn": "integer"
  }
}
```

### Common Response Patterns

#### Standard API Response

```json
{
  "success": "boolean",
  "message": "string",
  "data": "object (optional)"
}
```

#### Paginated Response

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "items": "array",
    "totalCount": "integer",
    "hasMore": "boolean"
  }
}
```

#### Error Response

```json
{
  "code": "string",
  "message": "string",
  "details": "object (optional)"
}
```

## Query Parameters

### Common Pagination Parameters

- `limit`: Integer (1-100) - Number of items to return
- `offset`: Integer (>=0) - Number of items to skip

### X-Ray List Parameters

- `type_filter`: String - Filter by X-Ray type (build, monitor, digest)
- `sort_by`: String - Sort order (created_at, updated_at, last_updated, title)

### Session Parameters

- `sessionID`: String - Session identifier
- `recurrenceID`: String - Recurrence identifier

## Authentication Requirements

Most endpoints require authentication via:

- **Bearer Token**: `Authorization: Bearer <token>`
- **Basic Auth**: For some service-to-service calls
- **Guest Tokens**: For guest user access

### Protected Endpoints

All endpoints except the following require authentication:

- Health check endpoints
- Some OAuth/auth initialization endpoints
- Public documentation endpoints

## Service-Specific Details

### Nebula Service (AI/ML)

**Base URL**: Internal service, accessed via Elio proxy
**Key Features**:

- Memory search and AI chat functionality
- Post-session summary generation
- X-Ray creation and management
- Meeting metadata processing

### Mars Service (Data)

**Base URL**: Internal service, accessed via Elio proxy
**Key Features**:

- Session and user data management
- Analytics and event tracking
- Billing integration
- Team management

### Luxor Service (Recordings & Integrations)

**Base URL**: Internal service, accessed via Elio proxy
**Key Features**:

- Session recording management
- Third-party integrations (Nango)
- File storage and retrieval

### Hubble Service (Auth)

**Base URL**: Internal service, accessed via Elio
**Key Features**:

- User authentication and authorization
- OAuth2 provider integration
- Policy-based access control
- Guest user management

## HTTP Status Codes

### Success Codes

- `200 OK` - Request successful
- `201 Created` - Resource created successfully
- `202 Accepted` - Request accepted for processing

### Client Error Codes

- `400 Bad Request` - Invalid request format or parameters
- `401 Unauthorized` - Authentication required or invalid
- `403 Forbidden` - Access denied
- `404 Not Found` - Resource not found
- `409 Conflict` - Resource conflict

### Server Error Codes

- `500 Internal Server Error` - Server error
- `502 Bad Gateway` - Upstream service error
- `503 Service Unavailable` - Service temporarily unavailable

## Rate Limiting

Most endpoints implement rate limiting:

- **Default**: 100 requests per minute per user
- **AI Endpoints**: 20 requests per minute per user
- **Auth Endpoints**: 10 requests per minute per IP

## WebSocket Endpoints

### Real-time Features

- **Audio Ingress**: `/v1.0/transcriptions/audio-ingress` - Real-time audio transcription
- **Lobby Enter**: `/v1.0/lobbies/enter` - Real-time lobby participation
- **Memory AI Stream**: `/v1.0/memory/ai-stream` - Streaming AI responses

## API Versioning

- **Current Version**: v1.0
- **Recording API**: v2.0 (specific to recording endpoints)
- **Versioning Strategy**: URL path versioning (`/v1.0/`, `/v2.0/`)

---

_Note: This specification is based on the current codebase structure. Request/response formats may vary slightly between services. Always refer to the specific API type definitions in the codebase for the most accurate schemas._
